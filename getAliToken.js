const axios = require('axios');
const crypto = require('crypto');
const uuid = require('uuid');

// ===== 重要提示 =====
// 请替换为您的阿里云访问密钥
const ACCESS_KEY_ID = 'LTAI5tAP67C7REeNmC2Wh1sv'; // 请替换为您的阿里云AccessKey ID
const ACCESS_KEY_SECRET = '******************************'; // 请替换为您的阿里云AccessKey Secret

// 构造请求参数
function createRequestParams() {
  return {
    AccessKeyId: ACCESS_KEY_ID,
    Action: 'CreateToken',
    Format: 'JSON',
    RegionId: 'cn-shanghai',
    SignatureMethod: 'HMAC-SHA1',
    SignatureNonce: uuid.v4(),
    SignatureVersion: '1.0',
    Timestamp: new Date().toISOString().replace(/\.\d{3}/, ''),
    Version: '2019-02-28'
  };
}

// URL编码函数，替换特殊字符
function encodeURIComponentRFC3986(str) {
  return encodeURIComponent(str)
    .replace(/!/g, '%21')
    .replace(/'/g, '%27')
    .replace(/\(/g, '%28')
    .replace(/\)/g, '%29')
    .replace(/\*/g, '%2A')
    .replace(/%20/g, '+');
}

// 构造规范化请求字符串
function canonicalizedQuery(params) {
  const sortedKeys = Object.keys(params).sort();
  return sortedKeys.map(key => {
    return `${encodeURIComponentRFC3986(key)}=${encodeURIComponentRFC3986(params[key])}`;
  }).join('&');
}

// 构造待签名字符串
function createStringToSign(method, urlPath, queryString) {
  return `${method}&${encodeURIComponentRFC3986(urlPath)}&${encodeURIComponentRFC3986(queryString)}`;
}

// 计算签名
function sign(stringToSign, secret) {
  const hmac = crypto.createHmac('sha1', `${secret}&`);
  return hmac.update(Buffer.from(stringToSign, 'utf-8')).digest('base64');
}

// 获取Token
async function getToken() {
  try {
    // 1. 构造请求参数
    const params = createRequestParams();
    
    // 2. 构造规范化请求字符串
    const queryString = canonicalizedQuery(params);
    
    // 3. 构造待签名字符串
    const stringToSign = createStringToSign('GET', '/', queryString);
    
    // 4. 计算签名
    const signature = sign(stringToSign, ACCESS_KEY_SECRET);
    
    // 5. 将签名加入到请求参数
    const finalParams = { ...params, Signature: signature };
    const finalQueryString = canonicalizedQuery(finalParams);
    
    // 6. 发送请求获取Token
    const url = `https://nls-meta.cn-shanghai.aliyuncs.com/?${finalQueryString}`;
    
    console.log('请求URL:', url);
    
    const response = await axios.get(url);
    
    if (response.data && response.data.Token) {
      const token = response.data.Token.Id;
      const expireTime = response.data.Token.ExpireTime;
      const expireDate = new Date(expireTime * 1000).toLocaleString();
      
      console.log('获取的Token:', token);
      console.log('Token有效期时间戳:', expireTime);
      console.log('Token有效期的北京时间:', expireDate);
      
      return {
        token,
        expireTime,
        expireDate
      };
    } else {
      console.error('获取Token失败:', response.data);
      return null;
    }
  } catch (error) {
    console.error('请求错误:', error.message);
    if (error.response && error.response.data) {
      console.error('响应数据:', error.response.data);
    }
    return null;
  }
}

// 使用示例
console.log("开始获取阿里云NLS服务的Token...");
console.log("注意：请确保您使用了正确的阿里云AccessKey ID和AccessKey Secret");
console.log("如果看到'Specified access key is not found'错误，说明提供的密钥无效或格式不正确");

module.exports = { getToken };

/*
相关文档：
- 阿里云智能语音交互服务: https://help.aliyun.com/product/30413.html
- 获取Token API: https://help.aliyun.com/zh/isi/getting-started/use-http-or-https-to-obtain-an-access-token
*/
