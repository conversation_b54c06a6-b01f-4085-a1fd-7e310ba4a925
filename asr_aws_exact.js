const {
  TranscribeStreamingClient,
  StartStreamTranscriptionCommand,
} = require("@aws-sdk/client-transcribe-streaming");

const getTime = () => {
  return new Date().toISOString().replace(/T|Z/g, ' ')
}

// Function to find the start of PCM data in a WAV file
function findPCMDataStart(buffer) {
  // Look for "data" chunk marker
  for (let i = 0; i < buffer.length - 4; i++) {
    if (buffer[i] === 0x64 && buffer[i + 1] === 0x61 && 
        buffer[i + 2] === 0x74 && buffer[i + 3] === 0x61) {
      // Found "data" - PCM data starts 8 bytes later (4 for "data" + 4 for size)
      return i + 8;
    }
  }
  return 44; // Default WAV header size if we can't find data chunk
}

class AsrClient {
  constructor(lang = 'zh') {
    this.lang = lang;
    this.globalCallback = () => {};
    this.isStarted = false;
    this.isStarting = false;
    this.client = null;
    this.currentStream = null;
    this.audioStreamController = null;
    this.headerSkipped = false;
    this.lastAsrText = '';

    // Use exact same credentials and setup as your working demo
    this.credentials = {
      accessKeyId: "********************",
      secretAccessKey: "3hU92jPZiFFEYsxNVfrpTFiIaUCGvAl3wj0iJsbj",
    };
  }

  async start() {
    if (this.isStarted || this.isStarting) {
      return true;
    }

    this.isStarting = true;

    try {
      // Initialize AWS client
      this.client = new TranscribeStreamingClient({
        region: "us-east-1",
        credentials: this.credentials,
        maxAttempts: 1,
      });

      // Start streaming transcription session
      await this.startStreamingSession();

      this.isStarted = true;
      this.isStarting = false;
      console.log(getTime(), 'aws asr started - streaming session ready');
      return true;
    } catch (error) {
      this.isStarting = false;
      console.error('AWS ASR start error:', error);
      return false;
    }
  }

  async end() {
    this.isStarted = false;
    this.headerSkipped = false;

    // Close current stream
    if (this.audioStreamController) {
      try {
        this.audioStreamController.close();
      } catch (e) {
        // Ignore close errors
      }
      this.audioStreamController = null;
    }

    this.currentStream = null;
    this.client = null;
  }

  async requestAsr(audioData, callback = () => {}, lang = null) {
    // Ensure we're started
    if (!this.isStarted && !this.isStarting) {
      await this.start();
    }

    if (this.isStarting) {
      // Wait for start to complete
      await new Promise(resolve => {
        const checkInterval = setInterval(() => {
          if (!this.isStarting) {
            clearInterval(checkInterval);
            resolve();
          }
        }, 10);
      });
    }

    this.globalCallback = callback;

    // Send audio data immediately to the stream (真正的流式处理)
    this.sendAudioToStream(audioData);
  }

  async startStreamingSession() {
    try {
      const languageCode = this.getLanguageCode(this.lang);

      // Create a streaming audio source
      const audioStream = this.createAudioStream();

      const params = {
        LanguageCode: languageCode,
        MediaEncoding: "pcm",
        MediaSampleRateHertz: 16000,
        AudioStream: audioStream,
      };

      console.log("📡 Starting streaming transcription session...");
      const command = new StartStreamTranscriptionCommand(params);
      const response = await this.client.send(command);

      this.currentStream = response;

      // Start listening for results in background
      this.listenForResults(response.TranscriptResultStream);

    } catch (err) {
      console.error("❌ Error starting streaming session:", err.message);
      throw err;
    }
  }

  createAudioStream() {
    // Create an async generator that we can push data to
    const audioQueue = [];
    let streamEnded = false;
    let waitingResolve = null;

    // Store methods to control the stream
    this.audioStreamController = {
      enqueue: (data) => {
        audioQueue.push(data);
        if (waitingResolve) {
          waitingResolve();
          waitingResolve = null;
        }
      },
      close: () => {
        streamEnded = true;
        if (waitingResolve) {
          waitingResolve();
          waitingResolve = null;
        }
      }
    };

    // Return async generator
    return (async function* () {
      while (!streamEnded || audioQueue.length > 0) {
        if (audioQueue.length > 0) {
          const chunk = audioQueue.shift();
          yield { AudioEvent: { AudioChunk: chunk } };
        } else if (!streamEnded) {
          // Wait for new data
          await new Promise(resolve => {
            waitingResolve = resolve;
          });
        }
      }
    })();
  }

  sendAudioToStream(audioData) {
    if (!this.audioStreamController) {
      console.log('AWS ASR: Stream not ready, skipping audio data');
      return;
    }

    try {
      // Skip WAV header on first chunk
      let dataToSend = audioData;
      if (!this.headerSkipped && audioData.length > 44) {
        const pcmDataStart = findPCMDataStart(audioData);
        if (pcmDataStart > 0) {
          console.log(`🔍 WAV header detected, PCM data starts at byte ${pcmDataStart}`);
          dataToSend = audioData.slice(pcmDataStart);
        }
        this.headerSkipped = true;
      }

      // Skip very small chunks (likely noise)
      if (dataToSend.length < 500) {
        return;
      }


      // Send audio data immediately to the stream
      this.audioStreamController.enqueue(dataToSend);

    } catch (error) {
      console.error('Error sending audio to stream:', error);
    }
  }

  async listenForResults(resultStream) {
    try {
      console.log("🎧 Listening for real-time transcription results...");

      for await (const event of resultStream) {
        if (event.TranscriptEvent) {
          const results = event.TranscriptEvent.Transcript.Results;

          if (results && results.length > 0) {
            results.forEach((result) => {
              if (result.Alternatives && result.Alternatives.length > 0) {
                const transcript = result.Alternatives[0].Transcript;
                const confidence = result.Alternatives[0].Confidence;
                const isFinal = !result.IsPartial;

                if (transcript && transcript.trim()) {
                  this.lastAsrText = transcript.trim();

                  const ret = {
                    isFinal: isFinal,
                    transcript: this.lastAsrText
                  };

                  console.log(getTime(), `transcript (${isFinal ? 'FINAL' : 'PARTIAL'}):`, this.lastAsrText);
                  this.globalCallback(ret);
                }
              }
            });
          }
        }
      }
    } catch (error) {
      console.error('Error listening for results:', error);
    }
  }

  // Old batch processing method - no longer used in streaming mode

  getLanguageCode(lang = 'zh') {
    const langMap = {
      'zh': 'zh-CN',
      'zh-cn': 'zh-CN',
      'en': 'en-US',
      'en-us': 'en-US',
      'ja': 'ja-JP',
      'ko': 'ko-KR',
      'fr': 'fr-FR',
      'de': 'de-DE',
      'es': 'es-ES',
      'it': 'it-IT',
      'pt': 'pt-BR',
      'ru': 'ru-RU',
      'ar': 'ar-SA',
      'hi': 'hi-IN'
    };
    
    return langMap[lang.toLowerCase()] || 'zh-CN';
  }
}

module.exports = {
  AsrClient,
};
